﻿using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 入库单明细基础输入参数
/// </summary>
public class WarehouseInrecordMXBaseInput
{
    /// <summary>
    /// 入库单ID
    /// </summary>
    public virtual long InrecordId { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    public virtual long GoodsId { get; set; }

    /// <summary>
    /// 品级
    /// </summary>
    public virtual RcvStatusEnum Rating { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? AuxiliaryUnit { get; set; }
    /// <summary>
    /// 数量
    /// </summary>
    public int? Conuantity { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public virtual DateTime? ProductDate { get; set; }

    /// <summary>
    /// 保质期
    /// </summary>
    public virtual int? Shelflife { get; set; }

    /// <summary>
    /// 保质期单位
    /// </summary>
    public virtual string? ShelflifeUnit { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public virtual DateTime? Expires { get; set; }

    /// <summary>
    /// 采购数量
    /// </summary>
    public virtual int? PuchQty { get; set; }

    /// <summary>
    /// 入库数量
    /// </summary>
    public virtual int? RcvQty { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public virtual long? SupplierId { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal Unitprice { get; set; }

    /// <summary>
    /// 合计
    /// </summary>
    public decimal TotalAmt { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public int DocumentNum { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    [SugarColumn(ColumnDescription = "软删除")]
    public virtual bool IsDelete { get; set; } = false;


}

/// <summary>
/// 入库单明细分页查询输入参数
/// </summary>
public class WarehouseInrecordMXInput : BasePageInput
{
    /// <summary>
    /// 入库单ID
    /// </summary>
    public long InrecordId { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    public long GoodsId { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProductDate { get; set; }

    /// <summary>
    /// 生产日期范围
    /// </summary>
    public List<DateTime?> ProductDateRange { get; set; }
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? Expires { get; set; }

    /// <summary>
    /// 过期时间范围
    /// </summary>
    public List<DateTime?> ExpiresRange { get; set; }
    /// <summary>
    /// 供应商ID
    /// </summary>
    public long? SupplierId { get; set; }

}

/// <summary>
/// 入库单明细增加输入参数
/// </summary>
public class AddWarehouseInrecordMXInput : WarehouseInrecordMXBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 入库单明细删除输入参数
/// </summary>
public class DeleteWarehouseInrecordMXInput : BaseIdInput
{
}

/// <summary>
/// 入库单明细更新输入参数
/// </summary>
public class UpdateWarehouseInrecordMXInput : WarehouseInrecordMXBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 入库单明细主键查询输入参数
/// </summary>
public class QueryByIdWarehouseInrecordMXInput : DeleteWarehouseInrecordMXInput
{

}
