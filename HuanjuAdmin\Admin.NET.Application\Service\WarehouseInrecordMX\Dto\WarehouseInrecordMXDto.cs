﻿using System;

namespace Admin.NET.Application;

    /// <summary>
    /// 入库单明细输出参数
    /// </summary>
    public class WarehouseInrecordMXDto
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public string WarehousegoodsName { get; set; }
        
        /// <summary>
        /// 供应商ID
        /// </summary>
        public string PubSupplierName { get; set; }
        
        /// <summary>
        /// Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 入库单ID
        /// </summary>
        public long InrecordId { get; set; }
        
        /// <summary>
        /// 商品ID
        /// </summary>
        public long GoodsId { get; set; }
        
        /// <summary>
        /// 品级
        /// </summary>
        public RcvStatusEnum Rating { get; set; }
        
        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }
        
        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime? ProductDate { get; set; }
        
        /// <summary>
        /// 保质期
        /// </summary>
        public int? Shelflife { get; set; }
        
        /// <summary>
        /// 保质期单位
        /// </summary>
        public string? ShelflifeUnit { get; set; }
        
        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? Expires { get; set; }
        
        /// <summary>
        /// 采购数量
        /// </summary>
        public int? PuchQty { get; set; }
        
        /// <summary>
        /// 入库数量
        /// </summary>
        public int? RcvQty { get; set; }
        
        /// <summary>
        /// 供应商ID
        /// </summary>
        public long? SupplierId { get; set; }
        
    }
