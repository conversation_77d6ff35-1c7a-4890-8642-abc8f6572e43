﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core.Util.Npoi;
using AngleSharp.Dom;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Magicodes.ExporterAndImporter.Core.Extension;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SqlSugar;
using System;
using System.Data;
using System.Linq;
//using NPOI.HPSF;

namespace Admin.NET.Application;
/// <summary>
/// 库存查询服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehouseStoreService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<WarehouseoutMX> _repwms;
    private readonly SqlSugarRepository<WarehouseInrecordMX> _repw;
    private readonly WarehousegoodsService _whg;
    private readonly WarehouseconvertrecordService _whs;
    private readonly SqlSugarRepository<WarehouseStore> _rep;
    private readonly SqlSugarRepository<WarehouseInrecord> _re;
    private readonly SqlSugarRepository<Warehouseout> _repwo;
    private readonly SqlSugarRepository<warehousebatch> _repbatch;
    private readonly SqlSugarRepository<InboundRecord> _repInBound;
    private readonly SqlSugarRepository<Entity.Warehouse> _repwh;
    private readonly SqlSugarRepository<WarehouseBatchUpdateQueue> _repbatchupdatequeue;
    public WarehouseStoreService(SqlSugarRepository<WarehouseStore> rep,
        SqlSugarRepository<WarehouseInrecord> re,
        WarehouseconvertrecordService whs,
        WarehousegoodsService whg,
        SqlSugarRepository<WarehouseInrecordMX> repw,
        SqlSugarRepository<WarehouseoutMX> repwms,
         SqlSugarRepository<Warehouseout> repwo,
         SqlSugarRepository<warehousebatch> repbatch,
         SqlSugarRepository<InboundRecord> repInBound,
         SqlSugarRepository<Entity.Warehouse> repwh,
         SqlSugarRepository<WarehouseBatchUpdateQueue> repbatchupdatequeue
         )
    {
        _repw = repw;
        _whg = whg;
        _rep = rep;
        _re = re;
        _whs = whs;
        _repwms = repwms;
        _repwo = repwo;
        _repbatch = repbatch;
        _repInBound = repInBound;
        _repwh = repwh;
        _repbatchupdatequeue = repbatchupdatequeue;
    }

    /// <summary>
    /// 分页查询库存查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehouseStoreOutput>> Page(WarehouseStoreInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Warehouse), u => u.WarehouseId.ToString() == input.Warehouse.Trim())
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Supplier), u => u.Supplier.Contains(input.Supplier.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.TradeName), u => u.Warehousegoods.Name.Contains(input.TradeName))
                    .WhereIF(input.StockOrNot != null, u => u.StockOrNot == input.StockOrNot)
                    .WhereIF(input.ExpiryStatus != null && input.ExpiryStatus.Any(), u => input.ExpiryStatus.Contains(u.ExpiredWarning ?? -1))
                    .WhereIF(input.StockStatus != null && input.StockStatus.Any(), u => input.StockStatus.Contains(u.StockWarning ?? -1))
                    .Select(u => new WarehouseStoreOutput
                    {
                        Id = u.Id,
                        BarCode = u.Warehousegoods.barcode,
                        Brand = u.Warehousegoods.Brand,
                        //Notes = u.Notes,
                        Number = u.Number,
                        Quantity = u.Quantity,
                        Unit = u.Unit,
                        UnitName = u.WarehouseGoodsUnit.Name,
                        CurrentCost = u.CurrentCost,
                        WarehouseId = u.WarehouseId,
                        TradeID = u.TradeID,
                        IsUniqueCode = u.IsUniqueCode ?? false,
                        ProduceTime = u.ProduceTime,
                        ProductCode = u.Warehousegoods.Code,
                        PurchaseUnitPrice = u.PurchaseUnitPrice,
                        SafetyStockTallNum = u.SafetyStockTallNum,
                        SafetyStockLowNum = u.SafetyStockLowNum,
                        SevenSalesNum = 0,
                        Specifications = u.Warehousegoods.Specs,
                        StockNum = 0,
                        Warranty = 0,
                        Warehouse = u.Warehouses.Name,
                        IsWarranty = u.IsWarranty,
                        TradeName = u.Warehousegoods.Name,
                        Supplier = u.Supplier,
                        AdventRemind = "",
                        GoodProduct = u.GoodProduct,
                        SalesOccupancy = u.SalesOccupancy ?? 0,
                        Marketable = u.Marketable ?? 0,
                        IntransitNum = u.IntransitNum ?? 0,
                        Compatible = u.Compatible ?? 0,
                        ShippedoutNum = u.ShippedoutNum ?? 0,
                        StockOrNot = u.StockOrNot ?? false,
                        ExpiredWarning = u.ExpiredWarning,
                        StockWarning = u.StockWarning,
                        isbatch = u.Warehousegoods.isbatch,
                        Reject = u.Reject
                    });

        query = query.OrderBuilder(input);
        var listWareHouseStore = await query.ToPagedListAsync(input.Page, input.PageSize);
        return listWareHouseStore;

        #region 已废弃
        //if (listWareHouseStore == null) return null;
        //var listWareHouseId = listWareHouseStore.Items.ToList().Select(x => x.WarehouseId).ToList();
        //var listGoodsId = listWareHouseStore.Items.ToList().Select(x => x.TradeID).ToList();

        //var listIncordMx = await _repw.AsQueryable()
        //    .LeftJoin<WarehouseInrecord>((u, incord) => u.InrecordId == incord.Id && incord.IsDelete == false && listWareHouseId.Contains(incord.Warehouseid) && (incord.InhouseStatus == RcvStatusEnum.Tobestored || incord.InhouseStatus == RcvStatusEnum.PartiallyReceived))
        //    .Where(u => u.IsDelete == false && listGoodsId.Contains(u.GoodsId))
        //        .Select((u, incord) => new WarehouseInrecordMXOutput
        //        {
        //            Id = u.Id,
        //            InrecordId = u.InrecordId,
        //            GoodsId = u.GoodsId,
        //            RcvQty = u.RcvQty,
        //            documentNum = u.DocumentNum,
        //            warehouseId = incord.Warehouseid,
        //        }).ToListAsync();

        //var listWarehouseOutMX = await _repwms.AsQueryable()
        //    .LeftJoin<Warehouseout>((u, warehouseOut) => u.warehouseout.WarehouseId == warehouseOut.Id && warehouseOut.IsDelete == false && listWareHouseId.Contains(warehouseOut.WarehouseId) && (warehouseOut.OutboundStatus == 1 || warehouseOut.OutboundStatus == 2))
        //    .Where(u => u.IsDelete == false)
        //    .Select(u => new WarehouseoutMXOutput
        //    {
        //        Id = u.Id,
        //        OutCount = u.OutCount,
        //        Tradename = u.Warehousegoods.Name,
        //        TrueOutCount = (int)u.TrueOutCount,
        //        GoodsId = u.goodsId,
        //        goodProduct = u.GoodProduct,
        //        WarehouseId = u.warehouseout.WarehouseId
        //    }).ToListAsync();

        //foreach (var item in listWareHouseStore.Items)
        //{
        //    var incordMX = listIncordMx.Where(x => x.warehouseId == item.WarehouseId && x.GoodsId == item.TradeID).ToList();
        //    if (incordMX != null)
        //    {
        //        item.IntransitNum = Convert.ToInt32(incordMX.Sum(x => x.documentNum != null ? incordMX.Sum(x => x.documentNum) : 0) - incordMX.Sum(x => x.RcvQty));
        //    }
        //    else
        //    {
        //        item.IntransitNum = 0;
        //    }

        //    var outputsMX = listWarehouseOutMX.Where(x => x.WarehouseId == item.WarehouseId).Where(s => s.GoodsId == item.TradeID).Where(x => x.goodProduct == true).ToList();
        //    if (outputsMX != null)
        //    {
        //        item.ShippedoutNum = Convert.ToInt32(outputsMX.Sum(x => x.OutCount != null ? outputsMX.Sum(x => x.OutCount) : 0) - outputsMX.Sum(x => x.TrueOutCount));
        //    }
        //    else
        //    {
        //        item.ShippedoutNum = 0;
        //    }

        //    item.Compatible = item.GoodProduct - item.ShippedoutNum;
        //}

        //if (input.StockOrNot != null)
        //{
        //    if (input.StockOrNot == true)
        //    {
        //        listWareHouseStore.Items = listWareHouseStore.Items.ToList().FindAll(x => x.Compatible < 0).ToList();
        //    }
        //    else if (input.StockOrNot == false)
        //    {
        //        listWareHouseStore.Items = listWareHouseStore.Items.ToList().FindAll(x => x.Compatible >= 0).ToList();
        //    }
        //}

        //return listWareHouseStore; 
        #endregion
    }

    /// <summary>
    /// 库存重算
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "RecalculateInventory")]
    public async Task RecalculateInventory(WarehouseStoreInput input)
    {
        var listWareHouseStore = await _rep.AsQueryable()
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Warehouse), u => u.WarehouseId.ToString() == input.Warehouse.Trim())
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Supplier), u => u.Supplier.Contains(input.Supplier.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.TradeName), u => u.Warehousegoods.Name.Contains(input.TradeName))
                    .Select(u => new WarehouseStoreOutput
                    {
                        Id = u.Id,
                        WarehouseId = u.WarehouseId,
                        TradeID = u.TradeID,
                        GoodProduct = u.GoodProduct,
                        SalesOccupancy = u.SalesOccupancy ?? 0,
                        Marketable = u.Marketable ?? 0,
                        IntransitNum = u.IntransitNum ?? 0,
                        Compatible = u.Compatible ?? 0,
                        ShippedoutNum = u.ShippedoutNum ?? 0,
                        StockOrNot = u.StockOrNot ?? false
                    }).ToListAsync();

        if (listWareHouseStore == null) return;
        var listWareHouseId = listWareHouseStore.Select(x => x.WarehouseId).ToList();
        var listGoodsId = listWareHouseStore.Select(x => x.TradeID).ToList();

        var listIncordMx = await _repw.AsQueryable()
            .InnerJoin<WarehouseInrecord>((u, incord) => u.InrecordId == incord.Id && incord.IsDelete == false && listWareHouseId.Contains(incord.Warehouseid) && (incord.InhouseStatus == RcvStatusEnum.Tobestored || incord.InhouseStatus == RcvStatusEnum.PartiallyReceived))
            .Where(u => u.IsDelete == false && listGoodsId.Contains(u.GoodsId))
                .Select((u, incord) => new WarehouseInrecordMXOutput
                {
                    Id = u.Id,
                    InrecordId = u.InrecordId,
                    GoodsId = u.GoodsId,
                    RcvQty = u.RcvQty,
                    documentNum = u.DocumentNum,
                    warehouseId = incord.Warehouseid,
                }).ToListAsync();

        var listWarehouseOutMX = await _repwms.AsQueryable()
            .InnerJoin<Warehouseout>((u, warehouseOut) => u.OutId == warehouseOut.Id && warehouseOut.IsDelete == false && listWareHouseId.Contains(warehouseOut.WarehouseId) && (warehouseOut.OutboundStatus == 1 || warehouseOut.OutboundStatus == 2))
            .Where(u => u.IsDelete == false)
            .Select(u => new WarehouseoutMXOutput
            {
                Id = u.Id,
                OutCount = u.OutCount,
                Tradename = u.Warehousegoods.Name,
                TrueOutCount = (int)u.TrueOutCount,
                GoodsId = u.goodsId,
                goodProduct = u.GoodProduct,
                WarehouseId = u.warehouseout.WarehouseId
            }).ToListAsync();

        foreach (var item in listWareHouseStore)
        {
            var incordMX = listIncordMx.Where(x => x.warehouseId == item.WarehouseId && x.GoodsId == item.TradeID).ToList();
            if (incordMX != null)
            {
                item.IntransitNum = Convert.ToInt32(incordMX.Sum(x => x.documentNum != null ? incordMX.Sum(x => x.documentNum) : 0) - incordMX.Sum(x => x.RcvQty));
            }
            else
            {
                item.IntransitNum = 0;
            }

            var outputsMX = listWarehouseOutMX.Where(x => x.WarehouseId == item.WarehouseId && x.GoodsId == item.TradeID && x.goodProduct).ToList();
            if (outputsMX != null)
            {
                item.ShippedoutNum = Convert.ToInt32(outputsMX.Sum(x => x.OutCount != null ? outputsMX.Sum(x => x.OutCount) : 0) - outputsMX.Sum(x => x.TrueOutCount));
            }
            else
            {
                item.ShippedoutNum = 0;
            }

            item.Compatible = item.GoodProduct - item.ShippedoutNum;
            item.StockOrNot = item.Compatible < 0;
        }

        var list = listWareHouseStore.Adapt<List<WarehouseStore>>();
        await _rep.AsUpdateable(list).UpdateColumns(x => new { x.IntransitNum, x.ShippedoutNum, x.Compatible, x.StockOrNot }).ExecuteCommandAsync();

        return;
    }

    /// <summary>
    /// 增加库存查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(List<Warehousing> input)
    {
        var incord = _re.GetById(input.FirstOrDefault()?.inrecordId);

        string orderNum = incord.OrderNumber + "-1";

        var inBound = _repInBound.AsQueryable()
                .LeftJoin<WarehouseInrecordMX>((u, mx) => u.WarehouseIncordMxId == mx.Id)
                .LeftJoin<WarehouseInrecord>((u, mx, i) => mx.InrecordId == i.Id)
                .Where((u, mx, i) => i.Id == incord.Id)
                .OrderByDescending((u, mx, i) => u.CreateTime).FirstAsync()?.Result;

        if (inBound != null && !inBound.OrderNum.IsNullOrWhiteSpace())
        {
            if (inBound.OrderNum.Contains('-'))
            {
                if (int.TryParse(inBound.OrderNum.Split('-')[1], out int currentNum))
                {
                    orderNum = incord.OrderNumber + "-" + (currentNum + 1).ToString();
                }
            }
        }

        foreach (var item in input)
        {
            var inwo = await _repw.GetByIdAsync(item.Id);
            if (inwo != null)
            {
                inwo.RcvQty = Convert.ToInt32(inwo.RcvQty) + item.quantity;
            }
            await _repw.UpdateAsync(inwo);

            var st = await _whg.GetCommodityByID(long.Parse(item.warehouse));
            var wareHouseStore = await GetWarehouse(item.goodsId, incord.Warehouseid);

            if ((bool)st.isbatch)
            {
                foreach (var batch in item.batchs)
                {
                    //如果批次商品有设置保质期预警，则计算批次库存
                    if (batch.ExpiryReminder.ToInt(0) > 0)
                    {
                        batch.ShelflifeStatus = 0;
                        var expiredTime = batch.ExpirationTime;
                        var expiryReminder = batch.ExpiryReminder.ToInt(0);
                        var currentDate = DateTime.Now.Date;
                        var expiryDate = currentDate.AddDays(expiryReminder);
                        if (expiryDate >= expiredTime)
                        {
                            batch.ShelflifeStatus = 1;
                        }
                        if (currentDate >= expiredTime)
                        {
                            batch.ShelflifeStatus = 2;
                        }
                    }
                    else
                    {
                        batch.ShelflifeStatus = -1;
                    }
                }
            }

            if (wareHouseStore != null)
            {
                wareHouseStore.Quantity = wareHouseStore.Quantity + item.quantity;
                wareHouseStore.CurrentCost = (item.Unitprice + wareHouseStore.CurrentCost) / 2;
                wareHouseStore.PurchaseUnitPrice = item.Unitprice;
                if (item.isproduct)
                {
                    wareHouseStore.GoodProduct += item.quantity;
                    wareHouseStore.Compatible = wareHouseStore.GoodProduct - wareHouseStore.ShippedoutNum.ToInt(0);
                    wareHouseStore.StockOrNot = wareHouseStore.Compatible.ToInt(0) < 0;

                    if (wareHouseStore.SafetyStockLowNum.ToInt(0) > 0 || wareHouseStore.SafetyStockTallNum.ToInt(0) > 0)
                    {
                        wareHouseStore.StockWarning = 0;
                        if (wareHouseStore.SafetyStockLowNum.ToInt(0) > 0 && wareHouseStore.GoodProduct.ToInt(0) < wareHouseStore.SafetyStockLowNum.ToInt(0))
                            //如果库存小于安全库存下限，则库存预警为1，否则为0 库存不足
                            wareHouseStore.StockWarning = 1;

                        if (wareHouseStore.SafetyStockTallNum.ToInt(0) > 0 && wareHouseStore.GoodProduct.ToInt(0) > wareHouseStore.SafetyStockTallNum.ToInt(0))
                            //如果库存大于安全库存上限，则库存预警为2，否则为0 库存超额
                            wareHouseStore.StockWarning = 2;
                    }
                    else
                    {
                        wareHouseStore.StockWarning = -1;
                    }

                    //取批次商品的保质期预警的最大值
                    if ((item.batchs.Max(x => x.ShelflifeStatus) ?? -1) > (wareHouseStore.ExpiredWarning ?? -1))
                        wareHouseStore.ExpiredWarning = item.batchs.Max(x => x.ShelflifeStatus);

                }
                else
                {
                    wareHouseStore.Reject = wareHouseStore.Reject + item.quantity;
                }

                wareHouseStore.IntransitNum = wareHouseStore.IntransitNum.ToInt(0) - item.quantity;

                await _rep.UpdateAsync(wareHouseStore);

                await InsertBatchOutInBound(st.isbatch, item.batchs, item.goodsId, item.isproduct, wareHouseStore.Id, inwo.Unitprice, inwo.Id, item.quantity, orderNum);
            }
            else
            {
                if (st == null)
                {
                    item.productCode = "";
                }
                else
                {
                    item.productCode = st.Code;
                    item.specifications = st.Specs;
                    item.brand = st.Brand;
                    item.tradeName = st.Name;
                }

                var whs = new WarehouseStore
                {
                    GoodProduct = item.isproduct ? item.quantity : 0,
                    Reject = item.isproduct ? 0 : item.quantity,
                    TradeID = item.goodsId,
                    WarehouseId = incord.Warehouseid,
                    IsUniqueCode = item.isUniqueCode,
                    //IsWarranty = item.isWarranty,
                    //Notes = item.notes,
                    ProduceTime = item.produceTime,
                    Quantity = item.quantity,
                    SafetyStockLowNum = Convert.ToInt32(item.safetyStockLowNum != null ? item.safetyStockLowNum : 0),
                    SafetyStockTallNum = Convert.ToInt32(item.safetyStockTallNum != null ? item.safetyStockTallNum : 0),
                    Supplier = item.supplier,
                    Unit = item.unit.ParseToLong(),
                    //Warehouse = item.warehouse,
                    Warranty = Convert.ToInt32(item.warranty),
                    //batchnumber = item.batchnumber,
                    PurchaseUnitPrice = item.Unitprice,
                    CurrentCost = item.Unitprice,
                    IntransitNum = item.documentNum - item.quantity,
                    Compatible = item.isproduct ? item.quantity : 0,
                    ExpiredWarning = item.batchs.Max(x => x.ShelflifeStatus)
                };
                var entity = whs.Adapt<WarehouseStore>();
                var addSuccess = await _rep.AsInsertable(entity).ExecuteCommandIdentityIntoEntityAsync();
                if (addSuccess)
                {
                    await InsertBatchOutInBound(st.isbatch, item.batchs, item.goodsId, item.isproduct, entity.Id, inwo.Unitprice, inwo.Id, item.quantity, orderNum);
                }
            }
        }
        if (input.Count > 0)
        {
            var inrecord = await _re.AsQueryable().Where(u => u.Id == input[0].inrecordId).FirstAsync();
            var inrecordMX = await _repw.GetListAsync(x => x.InrecordId == input[0].inrecordId);
            if (inrecordMX.Sum(x => x.DocumentNum) > inrecordMX.Sum(x => x.RcvQty) && inrecordMX.Sum(x => x.RcvQty) > 0)
            {
                inrecord.InhouseStatus = RcvStatusEnum.PartiallyReceived;
            }
            else if (inrecordMX.Sum(x => x.DocumentNum) <= inrecordMX.Sum(x => x.RcvQty))
            {
                inrecord.InhouseStatus = RcvStatusEnum.Received;
            }

            await _re.UpdateAsync(inrecord);
        }
    }

    async Task InsertBatchOutInBound(bool? isBatch, List<AddwarehousebatchInput> batchs, long goodId, bool isProduct, long inventoryId, decimal? unitPrice, long? wareHouseInMxId, int? inBoundCount, string orderNum)
    {
        if (isBatch == true && batchs != null)
        {
            foreach (var batch in batchs)
            {
                var rsHasBatch = _repbatch.AsQueryable()
                    .LeftJoin<WarehouseStore>((x, s) => x.InventoryId == s.Id)
                    .Where((x, s) => s.Warehousegoods.Id == goodId)
                    .Any(x => x.Batchnumber == batch.Batchnumber);
                if (rsHasBatch) throw new Exception($"批次号【{batch.Batchnumber}】已存在，请修改后重试");
                if (!isProduct)
                {
                    batch.RejectNum = batch.GoodProductNum;
                    batch.GoodProductNum = null;
                }
                batch.InventoryId = inventoryId;
                batch.Cost = unitPrice;
                var batchEntity = batch.Adapt<warehousebatch>();

                _repbatch.InsertReturnIdentity(batchEntity);

                //如果批次商品有保质期，则新增商品批次更新队列
                if (batch.ExpirationTime >= DateTime.Now.AddYears(-1))
                {
                    await App.GetRequiredService<WarehouseBatchQueueService>().AddBatchToQueue(batchEntity.Id, batch.ExpiryReminder.ToInt(0) > 0 ? batch.ExpirationTime.AddDays(0 - batch.ExpiryReminder.ToInt(0)) : null, batch.ExpirationTime);
                }

                var inBoundRecord = new InboundRecord();
                inBoundRecord.OrderNum = orderNum;
                inBoundRecord.WarehouseIncordMxId = wareHouseInMxId;
                inBoundRecord.InBoundCount = batch.GoodProductNum ?? batch.RejectNum;
                inBoundRecord.WarehouseBatchId = batchEntity.Id;
                _repInBound.Insert(inBoundRecord);
            }
        }
        else
        {
            var inBoundRecord = new InboundRecord();
            inBoundRecord.OrderNum = orderNum;
            inBoundRecord.WarehouseIncordMxId = wareHouseInMxId;
            inBoundRecord.InBoundCount = inBoundCount;
            inBoundRecord.WarehouseBatchId = null;
            _repInBound.Insert(inBoundRecord);
        }
    }

    /// <summary>
    /// 导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Import")]
    public async Task<FileContentResult> Import(WarehouseStoreInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Warehouse), u => u.WarehouseId.ToString() == input.Warehouse.Trim())
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Supplier), u => u.Supplier.Contains(input.Supplier.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.TradeName), u => u.Warehousegoods.Name.Contains(input.TradeName))
                    .Select(u => new WarehouseStoreOutput
                    {
                        Id = u.Id,
                        BarCode = u.Warehousegoods.barcode,
                        Brand = u.Warehousegoods.Brand,
                        Notes = u.Notes,
                        Number = u.Number,
                        Quantity = u.Quantity,
                        Unit = u.Unit,
                        UnitName = u.WarehouseGoodsUnit.Name,
                        CurrentCost = u.CurrentCost,
                        WarehouseId = u.WarehouseId,
                        TradeID = u.TradeID,
                        IsUniqueCode = u.IsUniqueCode ?? false,
                        // ShippedoutNum = _re.AsQueryable().Where(s => s.InhouseStatus == RcvStatusEnum.NotReceived).Count(),
                        ProduceTime = u.ProduceTime,
                        ProductCode = u.Warehousegoods.Code,
                        PurchaseUnitPrice = u.PurchaseUnitPrice,
                        SafetyStockTallNum = u.SafetyStockTallNum,
                        SafetyStockLowNum = u.SafetyStockLowNum,
                        //需查询出库单，待完成
                        SevenSalesNum = 0,
                        Specifications = u.Warehousegoods.Specs,
                        StockNum = 0,//u.Quantity-_re.AsQueryable().Where(s => s.InhouseStatus == RcvStatusEnum.NotReceived).Count() _re.AsQueryable().Where(s => s.InhouseStatus == RcvStatusEnum.NotReceived).Count(),
                        Warranty = 0,
                        Warehouse = u.Warehouses.Name,
                        IsWarranty = u.IsWarranty,
                        TradeName = u.Warehousegoods.Name,
                        Supplier = u.Supplier,
                        StockOrNot = u.StockOrNot ?? false,
                        AdventRemind = "",
                        GoodProduct = u.GoodProduct,
                        Reject = u.Reject,
                        Marketable = u.Marketable ?? 0,
                        SalesOccupancy = u.SalesOccupancy ?? 0,
                        Compatible = u.Compatible ?? 0,
                        ShippedoutNum = u.ShippedoutNum ?? 0,
                        IntransitNum = u.IntransitNum ?? 0,
                        isbatch = u.Warehousegoods.isbatch
                    });

        query = query.OrderBuilder(input);
        var sf = await query.ToListAsync();

        Dictionary<string, string> dicColumns = new Dictionary<string, string>
        {
            ["Number"] = "序号",
            ["Warehouse"] = "仓库",
            ["TradeName"] = "商品名称",
            ["ProductCode"] = "商品编码",
            ["Brand"] = "品牌",
            ["Specifications"] = "规格",
            ["UnitName"] = "单位",
            ["StockOrNot"] = "是否缺货",
            ["GoodProduct"] = "良品数量",
            ["Reject"] = "次品数量",
            ["Marketable"] = "可销售数量",
            ["SalesOccupancy"] = "销售占用数",
            ["Compatible"] = "可配数",
            ["ShippedoutNum"] = "待出库数",
            ["IntransitNum"] = "在途数",
            ["IsUniqueCode"] = "是否唯一码",
            ["SafetyStockTallNum"] = "最高库存数",
            ["SafetyStockLowNum"] = "最低库存数",
            ["PurchaseUnitPrice"] = "当前采购单价",
            ["CurrentCost"] = "当前成本",
            ["BarCode"] = "商品条码",
            ["isbatch"] = "是否批次"
        };
        List<WarehouseStoreOutput> lw = new List<WarehouseStoreOutput>();
        var res = sf;
        var export = NpoiHelper.ExportExcel(res, dicColumns);
        var mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        return new FileContentResult(export, mimeType)
        {
            // FileDownloadName = fileName
        };
    }



    /// <summary>
    /// 删除库存查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWarehouseStoreInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新库存查询（设置安全库存）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehouseStoreInput input)
    {
        var entity = input.Adapt<WarehouseStore>();

        if (entity.SafetyStockLowNum.ToInt(0) > 0 || entity.SafetyStockTallNum.ToInt(0) > 0)
        {
            entity.StockWarning = 0;
            if (entity.SafetyStockLowNum.ToInt(0) > 0 && entity.GoodProduct.ToInt(0) < entity.SafetyStockLowNum.ToInt(0))
                //如果库存小于安全库存下限，则库存预警为1，否则为0 库存不足
                entity.StockWarning = 1;

            if (entity.SafetyStockTallNum.ToInt(0) > 0 && entity.GoodProduct.ToInt(0) > entity.SafetyStockTallNum.ToInt(0))
                //如果库存大于安全库存上限，则库存预警为2，否则为0 库存超额
                entity.StockWarning = 2;
        }
        else
        {
            entity.StockWarning = -1;
        }

        await _rep.AsUpdateable(entity).UpdateColumns(x => new { x.SafetyStockLowNum, x.SafetyStockTallNum, x.StockWarning }).ExecuteCommandAsync();

    }

    /// <summary>
    /// 属性转换
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "AttributeConvert")]
    public async Task AttributeConvert(UpdateWarehouseStoreInput input)
    {
        input.Unit = null;
        var notes = input.Notes;
        var batchNumber = "";
        input.Notes = null;
        //var entity = input.Adapt<WarehouseStore>();
        var entity = _rep.GetByIdAsync(input.Id).Result;
        if (input.type == 1)//良转次
        {
            entity.GoodProduct -= input.connvertNum.ToInt(0);
            entity.Reject += input.connvertNum.ToInt(0);
        }
        else//次转良
        {
            entity.GoodProduct += input.connvertNum.ToInt(0);
            entity.Reject -= input.connvertNum.ToInt(0);
        }
        entity.Compatible = entity.GoodProduct - entity.ShippedoutNum.ToInt(0);
        entity.StockOrNot = entity.Compatible < 0;

        if (entity.SafetyStockLowNum.ToInt(0) > 0 || entity.SafetyStockTallNum.ToInt(0) > 0)
        {
            entity.StockWarning = 0;
            if (entity.SafetyStockLowNum.ToInt(0) > 0 && entity.GoodProduct.ToInt(0) < entity.SafetyStockLowNum.ToInt(0))
                //如果库存小于安全库存下限，则库存预警为1，否则为0 库存不足
                entity.StockWarning = 1;

            if (entity.SafetyStockTallNum.ToInt(0) > 0 && entity.GoodProduct.ToInt(0) > entity.SafetyStockTallNum.ToInt(0))
                //如果库存大于安全库存上限，则库存预警为2，否则为0 库存超额
                entity.StockWarning = 2;
        }
        else
        {
            entity.StockWarning = -1;
        }

        if (input.isbatch.GetValueOrDefault(false) && input.connvertNum > 0)
        {
            //处理批次
            var batch = _repbatch.AsQueryable().First(u => u.InventoryId == input.Id && u.Id == input.BatchId);
            var ckNum = input.connvertNum;
            if (input.type == 1)//良转次
            {
                batch.RejectNum = batch.RejectNum.GetValueOrDefault(0) + ckNum;
                batch.GoodProductNum = batch.GoodProductNum.GetValueOrDefault(0) - ckNum;

                if (batch.GoodProductNum.GetValueOrDefault(0) <= 0)
                {
                    batch.ShelflifeStatus = -1;
                    var shelflifeStatus = await _repbatch.AsQueryable().Where(u => u.InventoryId == entity.Id && u.Id != batch.Id && u.GoodProductNum > 0).MaxAsync(x => x.ShelflifeStatus);
                    entity.ExpiredWarning = shelflifeStatus ?? -1;

                    await _repbatchupdatequeue.DeleteAsync(u => u.BatchId == batch.Id);
                }
                await _repbatch.UpdateAsync(batch);
            }
            else//次转良
            {
                if (batch.GoodProductNum.GetValueOrDefault(0) <= 0 && ckNum > 0)
                {
                    if (batch.ExpirationTime >= DateTime.Now.AddYears(-1))
                    {
                        //如果批次商品有设置保质期预警，则计算批次库存
                        if (batch.ExpiryReminder.ToInt(0) > 0)
                        {
                            batch.ShelflifeStatus = 0;
                            var expiredTime = batch.ExpirationTime;
                            var expiryReminder = batch.ExpiryReminder.ToInt(0);
                            var currentDate = DateTime.Now.Date;
                            var expiryDate = currentDate.AddDays(expiryReminder);
                            if (expiryDate >= expiredTime)
                            {
                                batch.ShelflifeStatus = 1;
                            }
                            if (currentDate >= expiredTime)
                            {
                                batch.ShelflifeStatus = 2;
                            }
                        }
                        else
                        {
                            batch.ShelflifeStatus = -1;
                        }

                        await App.GetRequiredService<WarehouseBatchQueueService>().AddBatchToQueue(batch.Id, batch.ExpiryReminder.ToInt(0) > 0 ? batch.ExpirationTime.AddDays(0 - batch.ExpiryReminder.ToInt(0)) : null, batch.ExpirationTime);
                    }
                }
                batch.RejectNum = batch.RejectNum.GetValueOrDefault(0) - ckNum;
                batch.GoodProductNum = batch.GoodProductNum.GetValueOrDefault(0) + ckNum;
                await _repbatch.UpdateAsync(batch);
            }
            batchNumber = batch.Batchnumber;
        }
        var warehouse = await _repwh.GetFirstAsync(u => u.Id == entity.WarehouseId);
        AddWarehouseconvertrecordInput aw = new AddWarehouseconvertrecordInput();
        aw.Commodity = input.TradeName;
        aw.Num = input.connvertNum;
        aw.Notes = notes;
        aw.Type = input.type;
        aw.Warehouse = warehouse.Name;
        aw.BatchNumber = batchNumber;
        aw.UniqueCode = "";
        await _whs.Add(aw);

        var result = await _rep.AsUpdateable(entity).UpdateColumns(x => new { x.GoodProduct, x.Reject, x.Compatible, x.StockOrNot, x.StockWarning, x.ExpiredWarning }).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取库存查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WarehouseStore> Get([FromQuery] QueryByIdWarehouseStoreInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }


    /// <summary>
    /// 获取查询
    /// 
    /// </summary>
    /// <param name="trandId"></param>
    /// <param name="warehouse"></param>
    /// <returns></returns>
    public async Task<WarehouseStore> GetWarehouse(long trandId, long? warehouse)
    {
        return await _rep.GetFirstAsync(u => u.Warehousegoods.Id == trandId && u.WarehouseId == warehouse);
    }

    /// <summary>
    /// 获取库存查询列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehouseStoreOutput>> List([FromQuery] WarehouseStoreInput input)
    {
        return await _rep.AsQueryable().Select<WarehouseStoreOutput>().ToListAsync();
    }





}

