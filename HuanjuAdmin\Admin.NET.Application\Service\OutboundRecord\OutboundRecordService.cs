using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.OutboundRecord.Dto;
using Admin.NET.Core;
using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Admin.NET.Application;
/// <summary>
/// 出库记录服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class OutboundRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<OutboundRecord> _rep;
    private readonly SqlSugarRepository<Warehouseout> _repout;
    private readonly SqlSugarRepository<Warehousegoods> _repGoods;
    private readonly SqlSugarRepository<OutInBoundRecord> _repOI;
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<OutboundRecord> _repO;
    private readonly SqlSugarRepository<InboundRecord> _repI;
    private readonly SqlSugarRepository<WarehouseoutMX> _repmx;
    private readonly SqlSugarRepository<WarehouseStore> _repStore;
    private readonly SqlSugarRepository<warehousebatch> _repbatch;
    private readonly SqlSugarRepository<WarehouseBatchUpdateQueue> _repWarehouseBatchUpdateQueue;
    private readonly SqlSugarRepository<WarehouseInrecord> _repIn;
    private readonly SqlSugarRepository<WarehouseInrecordMX> _repInMx;
    public OutboundRecordService(SqlSugarRepository<OutboundRecord> rep, SqlSugarRepository<OutInBoundRecord> repOI,SqlSugarRepository<Warehouseout> repout,SqlSugarRepository<Warehousegoods> repGoods, UserManager userManager, SqlSugarRepository<OutboundRecord> repO, SqlSugarRepository<InboundRecord> repI, SqlSugarRepository<WarehouseoutMX> repmx, SqlSugarRepository<WarehouseStore> repStore, SqlSugarRepository<warehousebatch> repbatch, SqlSugarRepository<WarehouseBatchUpdateQueue> repWarehouseBatchUpdateQueue, SqlSugarRepository<WarehouseInrecord> repIn, SqlSugarRepository<WarehouseInrecordMX> repInMx)
    {
        _rep = rep;
        _repOI = repOI;
        _repout = repout;
        _repGoods = repGoods;
        _userManager = userManager;
        _repO = repO;
        _repI = repI;
        _repmx = repmx;
        _repStore = repStore;
        _repbatch = repbatch;
        _repWarehouseBatchUpdateQueue = repWarehouseBatchUpdateQueue;
        _repIn = repIn;
        _repInMx = repInMx;
    }

    /// <summary>
    /// 分页查询出入库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<OutInBoundOutput>> Page(OutboundRecordInput input)
    {
        var query = _repOI.AsQueryable()
            .Where(u => u.TenantId == _userManager.TenantId && u.IsDelete == false)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Type), u => u.Type == input.Type)
            .WhereIF(!string.IsNullOrWhiteSpace(input.PrintState), u => input.PrintState == "0" ? u.PrintCount == 0 : u.PrintCount > 0)
            .WhereIF(!string.IsNullOrWhiteSpace(input.OrderNumber), u => u.OrderNumber == input.OrderNumber)
            .WhereIF(input.CustomId != null, u => u.CustomId == input.CustomId)
            .WhereIF(input.SupplierId != null, u => u.SupplierId == input.SupplierId)
            .WhereIF(input.WarehouseId != null, u => u.WarehouseId == input.WarehouseId)
            .WhereIF(input.GoodsId != null, u => u.GoodsId == input.GoodsId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.BatchNumber), u => u.BatchNumber == input.BatchNumber)
            .WhereIF(input.StartTime != null, u => u.CreateTime >= input.StartTime)
            .WhereIF(input.EndTime != null, u => u.CreateTime < input.EndTime)
            .WhereIF(input.CreateUserId != null, u => u.CreateUserId < input.CreateUserId)
            .Select<OutInBoundOutput>();
        query = query.OrderBuilder(input);
        var list = await query.ToPagedListAsync(input.Page, input.PageSize);
        return list;
    }

    /// <summary>
    /// 分页查询出入库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<OutInBoundOutput>> List(OutInBoundInput input)
    {
        var query = _repOI.AsQueryable()
            .Where(u => u.TenantId == _userManager.TenantId && u.IsDelete == false)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Type), u => u.Type == input.Type)
            .WhereIF(!string.IsNullOrWhiteSpace(input.OrderNumber), u => u.OrderNumber == input.OrderNumber)
            .OrderByDescending(u => u.CreateTime)
            .Select<OutInBoundOutput>();
        var list = await query.ToListAsync();
        return list;
    }

    /// <summary>
    /// 增加出库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddOutboundRecordInput input)
    {
        var entity = input.Adapt<OutboundRecord>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除出库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteOutboundRecordInput input)
    {
        var entity = input.Adapt<OutboundRecord>();
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新出库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateOutboundRecordInput input)
    {
        var entity = input.Adapt<OutboundRecord>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取出库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<OutboundRecord> Get([FromQuery] QueryByIdOutboundRecordInput input)
    {
        return await _rep.GetByIdAsync(input.Id);
    }


    /// <summary>
    /// 更新打印次数
    /// </summary>
    /// <param name="records"></param>
    /// <returns></returns>   
    [HttpPost]
    [ApiDescriptionSettings(Name = "UpdatePrintCount")]
    public async Task UpdatePrintCountAsync([FromBody] List<PrintRecord> records)
    {
        List<long> listO = new List<long>();
        List<long> listI = new List<long>();
        if (records != null)
        {
            foreach (var item in records)
            {
                if (item.Type == "0")
                {
                    listO.Add(item.Id);
                }
                else if (item.Type == "1")
                {
                    listI.Add(item.Id);
                }
            }
        }

        if (listO.Count > 0)
        {
            await _repO.AsUpdateable()
                .SetColumns(it => new OutboundRecord
                {
                    PrintCount = it.PrintCount + 1,
                    UpdateUserId = _userManager.UserId,
                    UpdateTime = SqlFunc.GetDate()
                })
                .Where(x => listO.Contains(x.Id)).ExecuteCommandAsync();
        }

        if (listI.Count > 0)
        {
            await _repI.AsUpdateable().
                SetColumns(it => new InboundRecord
                {
                    PrintCount = it.PrintCount + 1,
                    UpdateUserId = _userManager.UserId,
                    UpdateTime = SqlFunc.GetDate()
                })
                .Where(x => listI.Contains(x.Id)).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 出库记录红冲
    /// </summary>
    /// <param name="outboundRecordId">需要红冲的出库记录ID</param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "RedInkOutboundRecord")]
    public async Task RedInkOutboundRecord(long outboundRecordId)
    {
        // 获取原出库记录
        var outboundRecord = await _rep.GetFirstAsync(x => x.Id == outboundRecordId)
            ?? throw Oops.Oh("未找到出库记录");

        // 获取出库明细
        var outMx = await _repmx.GetFirstAsync(x => x.Id == outboundRecord.WarehouseOutMxId)
            ?? throw Oops.Oh("未找到对应的出库明细");

        // 获取出库单
        var outOrder = await _repout.GetFirstAsync(x => x.Id == outMx.OutId)
            ?? throw Oops.Oh("未找到对应的出库单");

        // 验证是否可以红冲
        if (outOrder.OutboundStatus != 2 && outOrder.OutboundStatus != 3)
            throw Oops.Oh("只有部分出库或完全出库的单据才能进行红冲操作");

        // 生成红冲记录编号
        string redInkOrderNum = outboundRecord.OrderNum + "-红冲";

        // 获取库存信息 - 使用WarehouseStore类型
        var querysale = await _repStore.GetFirstAsync(x => x.TradeID == outMx.goodsId && x.WarehouseId == outOrder.WarehouseId)
            ?? throw Oops.Oh($"未找到商品ID为{outMx.goodsId}的库存信息");

        // 获取商品信息
        var goods = await _repGoods.GetByIdAsync(outMx.goodsId);

        // 红冲数量
        int redInkCount = outboundRecord.OutBoundCount.ToInt(0);

        // 出库明细实际出库数量调整
        outMx.TrueOutCount = outMx.TrueOutCount.ToInt(0) - redInkCount;
        await _repmx.UpdateAsync(outMx);

        // 处理库存 - 红冲时增加库存
        if (outMx.GoodProduct)
        {
            querysale.GoodProduct = querysale.GoodProduct.ToInt(0) + redInkCount;
            querysale.Quantity = querysale.Quantity.ToInt(0) + redInkCount;
            querysale.ShippedoutNum = querysale.ShippedoutNum.ToInt(0) + redInkCount;

            // 更新安全库存预警状态
            if (querysale.SafetyStockLowNum.ToInt(0) > 0 || querysale.SafetyStockTallNum.ToInt(0) > 0)
            {
                querysale.StockWarning = 0;
                if (querysale.SafetyStockLowNum.ToInt(0) > 0 && querysale.GoodProduct.ToInt(0) < querysale.SafetyStockLowNum.ToInt(0))
                    querysale.StockWarning = 1; // 库存不足

                if (querysale.SafetyStockTallNum.ToInt(0) > 0 && querysale.GoodProduct.ToInt(0) > querysale.SafetyStockTallNum.ToInt(0))
                    querysale.StockWarning = 2; // 库存超额
            }
            else
            {
                querysale.StockWarning = -1;
            }
        }
        else
        {
            querysale.Reject = querysale.Reject.ToInt(0) + redInkCount;
            querysale.Quantity = querysale.Quantity.ToInt(0) + redInkCount;
        }

        // 处理批次信息
        if (goods?.isbatch == true && outboundRecord.WarehouseBatchId.HasValue)
        {
            // 更新批次库存
            var batch = await _repbatch.GetByIdAsync(outboundRecord.WarehouseBatchId.Value);
            if (batch != null)
            {
                if (outMx.GoodProduct)
                    batch.GoodProductNum = batch.GoodProductNum.ToInt(0) + redInkCount;
                else
                    batch.RejectNum = batch.RejectNum.ToInt(0) + redInkCount;

                // 更新批次状态
                if (batch.GoodProductNum > 0 || batch.RejectNum > 0)
                {
                    batch.ShelflifeStatus = 0; // 重置批次状态

                    // 添加批次更新队列
                    if (!await _repWarehouseBatchUpdateQueue.IsAnyAsync(x => x.BatchId == batch.Id))
                    {
                        await _repWarehouseBatchUpdateQueue.InsertAsync(new WarehouseBatchUpdateQueue { BatchId = batch.Id });
                    }
                }

                await _repbatch.UpdateAsync(batch);
            }

            // 更新库存的保质期预警状态
            var activeBatches = await _repbatch.AsQueryable()
                .Where(u => u.InventoryId == querysale.Id && u.GoodProductNum > 0)
                .ToListAsync();

            querysale.ExpiredWarning = activeBatches.Any()
                ? activeBatches.Max(x => x.ShelflifeStatus) ?? -1
                : -1;
        }

        // 创建红冲出库记录
        var redInkOutboundRecord = new OutboundRecord
        {
            OrderNum = redInkOrderNum,
            WarehouseOutMxId = outMx.Id,
            OutBoundCount = -redInkCount, // 负数表示红冲
            WarehouseBatchId = outboundRecord.WarehouseBatchId,
            TenantId = _userManager.TenantId
        };

        // 添加备注字段（如果OutboundRecord有此字段）
        redInkOutboundRecord.Remark = $"红冲记录，原记录ID: {outboundRecordId}";

        await _rep.InsertAsync(redInkOutboundRecord);

        // 更新库存
        await _repStore.UpdateAsync(querysale);

        // 更新出库单状态
        var allMxList = await _repmx.GetListAsync(x => x.OutId == outOrder.Id && x.IsDelete == false);
        var totalOutCount = allMxList.Sum(x => x.OutCount.ToInt(0));
        var totalTrueOutCount = allMxList.Sum(x => x.TrueOutCount.ToInt(0));

        if (totalTrueOutCount == 0)
        {
            outOrder.OutboundStatus = 1; // 已提交
        }
        else if (totalTrueOutCount < totalOutCount)
        {
            outOrder.OutboundStatus = 2; // 部分出库
        }
        else
        {
            outOrder.OutboundStatus = 3; // 完全出库
        }

        await _repout.UpdateAsync(outOrder);

        // 更新相关业务单据状态
        if (!string.IsNullOrEmpty(outOrder.SuperiorNum))
        {
            await App.GetRequiredService<WarehouseoutService>().UpdateStatus(outOrder.SuperiorNum, outOrder.OutboundStatus);
        }
    }

    /// <summary>
    /// 入库记录红冲
    /// </summary>
    /// <param name="inboundRecordId">需要红冲的入库记录ID</param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "RedInkInboundRecord")]
    public async Task RedInkInboundRecord(long inboundRecordId)
    {
        // 获取原入库记录
        var inboundRecord = await _repI.GetFirstAsync(x => x.Id == inboundRecordId)
            ?? throw Oops.Oh("未找到入库记录");

        // 获取入库明细
        var inMx = await _repInMx.GetFirstAsync(x => x.Id == inboundRecord.WarehouseIncordMxId)
            ?? throw Oops.Oh("未找到对应的入库明细");

        // 获取入库单
        var inOrder = await _repIn.GetFirstAsync(x => x.Id == inMx.InrecordId)
            ?? throw Oops.Oh("未找到对应的入库单");

        // 验证是否可以红冲
        if (inOrder.InhouseStatus != RcvStatusEnum.PartiallyReceived && inOrder.InhouseStatus != RcvStatusEnum.Received)
            throw Oops.Oh("只有部分入库或完全入库的单据才能进行红冲操作");

        // 生成红冲记录编号
        string redInkOrderNum = inboundRecord.OrderNum + "-红冲";

        // 获取库存信息
        var querysale = await _repStore.GetFirstAsync(x => x.TradeID == inMx.GoodsId && x.WarehouseId == inOrder.Warehouseid)
            ?? throw Oops.Oh($"未找到商品ID为{inMx.GoodsId}的库存信息");

        // 获取商品信息
        var goods = await _repGoods.GetByIdAsync(inMx.GoodsId);

        // 红冲数量
        int redInkCount = inboundRecord.InBoundCount.ToInt(0);

        // 入库明细实际入库数量调整
        inMx.DocumentNum = inMx.DocumentNum.ToInt(0) - redInkCount;
        await _repInMx.UpdateAsync(inMx);

        // 处理库存 - 红冲时减少库存
        if (inMx.GoodProduct)
        {
            // 检查是否有足够库存
            if (querysale.GoodProduct.ToInt(0) < redInkCount)
                throw Oops.Oh($"商品[{goods?.Name}]库存不足，无法执行红冲操作");

            querysale.GoodProduct = querysale.GoodProduct.ToInt(0) - redInkCount;
            querysale.Quantity = querysale.Quantity.ToInt(0) - redInkCount;

            // 更新安全库存预警状态
            if (querysale.SafetyStockLowNum.ToInt(0) > 0 || querysale.SafetyStockTallNum.ToInt(0) > 0)
            {
                querysale.StockWarning = 0;
                if (querysale.SafetyStockLowNum.ToInt(0) > 0 && querysale.GoodProduct.ToInt(0) < querysale.SafetyStockLowNum.ToInt(0))
                    querysale.StockWarning = 1; // 库存不足

                if (querysale.SafetyStockTallNum.ToInt(0) > 0 && querysale.GoodProduct.ToInt(0) > querysale.SafetyStockTallNum.ToInt(0))
                    querysale.StockWarning = 2; // 库存超额
            }
            else
            {
                querysale.StockWarning = -1;
            }
        }
        else
        {
            // 检查是否有足够库存
            if (querysale.Reject.ToInt(0) < redInkCount)
                throw Oops.Oh($"商品[{goods?.Name}]不良品库存不足，无法执行红冲操作");

            querysale.Reject = querysale.Reject.ToInt(0) - redInkCount;
            querysale.Quantity = querysale.Quantity.ToInt(0) - redInkCount;
        }

        // 处理批次信息
        if (goods?.isbatch == true && inboundRecord.WarehouseBatchId.HasValue)
        {
            // 更新批次库存
            var batch = await _repbatch.GetByIdAsync(inboundRecord.WarehouseBatchId.Value);
            if (batch != null)
            {
                if (inMx.GoodProduct)
                {
                    // 检查批次库存是否足够
                    if (batch.GoodProductNum.ToInt(0) < redInkCount)
                        throw Oops.Oh($"商品[{goods.Name}]批次[{batch.Batchnumber}]库存不足，无法执行红冲操作");

                    batch.GoodProductNum = batch.GoodProductNum.ToInt(0) - redInkCount;
                }
                else
                {
                    // 检查批次库存是否足够
                    if (batch.RejectNum.ToInt(0) < redInkCount)
                        throw Oops.Oh($"商品[{goods.Name}]批次[{batch.Batchnumber}]不良品库存不足，无法执行红冲操作");

                    batch.RejectNum = batch.RejectNum.ToInt(0) - redInkCount;
                }

                // 更新批次状态
                if (batch.GoodProductNum <= 0 && batch.RejectNum <= 0)
                {
                    batch.ShelflifeStatus = -1; // 批次库存为空
                    await _repWarehouseBatchUpdateQueue.DeleteAsync(x => x.BatchId == batch.Id);
                }
                else
                {
                    // 添加批次更新队列
                    if (!await _repWarehouseBatchUpdateQueue.IsAnyAsync(x => x.BatchId == batch.Id))
                    {
                        await _repWarehouseBatchUpdateQueue.InsertAsync(new WarehouseBatchUpdateQueue { BatchId = batch.Id });
                    }
                }

                await _repbatch.UpdateAsync(batch);
            }

            // 更新库存的保质期预警状态
            var activeBatches = await _repbatch.AsQueryable()
                .Where(u => u.InventoryId == querysale.Id && u.GoodProductNum > 0)
                .ToListAsync();

            querysale.ExpiredWarning = activeBatches.Any()
                ? activeBatches.Max(x => x.ShelflifeStatus) ?? -1
                : -1;
        }

        // 创建红冲入库记录
        var redInkInboundRecord = new InboundRecord
        {
            OrderNum = redInkOrderNum,
            WarehouseIncordMxId = inMx.Id,
            InBoundCount = -redInkCount, // 负数表示红冲
            WarehouseBatchId = inboundRecord.WarehouseBatchId,
            TenantId = _userManager.TenantId,
            Remark = $"红冲记录，原记录ID: {inboundRecordId}"
        };

        await _repI.InsertAsync(redInkInboundRecord);

        // 更新库存
        await _repStore.UpdateAsync(querysale);

        // 更新入库单状态
        var allMxList = await _repInMx.GetListAsync(x => x.InrecordId == inOrder.Id && x.IsDelete == false);

        // 计算总数量和实际入库数量
        var totalInCount = allMxList.Sum(x => x.InCount.ToInt(0));
        var totalTrueInCount = allMxList.Sum(x => x.TrueInCount.ToInt(0));

        // 根据实际入库情况调整状态
        if (totalTrueInCount == 0)
        {
            inOrder.Status = 1; // 已提交
        }
        else if (totalTrueInCount < totalInCount)
        {
            inOrder.Status = 2; // 部分入库
        }
        else
        {
            inOrder.Status = 3; // 完全入库
        }

        await _repIn.UpdateAsync(inOrder);

        // 更新相关业务单据状态
        if (!string.IsNullOrEmpty(inOrder.RelationNum))
        {
            await App.GetRequiredService<WarehouseinrecordService>().UpdateStatus(inOrder.RelationNum, inOrder.Status);
        }
    }

    public class PrintRecord
    {
        public long Id { get; set; }
        public string Type { get; set; }
    }
}

