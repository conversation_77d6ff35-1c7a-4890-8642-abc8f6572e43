﻿using Admin.NET.Application.Service.OutboundRecord.Dto;
using System;

namespace Admin.NET.Application;

    /// <summary>
    /// 商品出库明细输出参数
    /// </summary>
    public class WarehouseoutMXOutput
    {
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 是否良品
    /// </summary>
    public bool goodProduct { get; set; } 
    /// <summary>
    /// 商品名称
    /// </summary>
    public string? Tradename { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string? Barcode { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string? Productcode { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Specifications { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public long? Unit { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal Unitprice { get; set; }

    /// <summary>
    /// 合计
    /// </summary>
    public decimal TotalAmt { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 是否缺货
    /// </summary>
    public bool vacancy { get; set; }


    /// <summary>
    /// 商品Id
    /// </summary>
    public long GoodsId { get; set; }

    /// <summary>
    /// 出库数量
    /// </summary>
    public int? OutCount { get; set; }

    /// <summary>
    /// 辅助单位数量
    /// </summary>
    public int auxiliaryOutCount { get; set; }

    /// <summary>
    /// 辅助单位
    /// </summary>
    public string auxiliaryunit { get; set; }

    /// <summary>
    /// 实际出库数量
    /// </summary>
    public int TrueOutCount { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// 出库单ID
    /// </summary>
    public long OutId { get; set; }



    /// <summary>
    /// 出库时间
    /// </summary>
    public DateTime? Deliverytime { get; set; }

    public  long? TenantId { get; set; }

    public bool IsDelete { get; set; }


    public int? convertCount { get; set; }

    /// <summary>
    /// 仓库ID
    /// </summary>
    public long WarehouseId { get; set; }
    public string WarehouseName { get; set; }


    /// <summary>
    /// 出库记录
    /// </summary>
    public List<OutInBoundOutput> listOutInBound { get; set; }
}


