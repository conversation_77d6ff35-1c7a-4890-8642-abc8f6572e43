﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.OutboundRecord.Dto;
public class OutInBoundOutput
{
    //主键
    public long Id { get; set; }

    /// <summary>
    /// 出入库类型
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    /// 上级ID
    /// </summary>
    public long ParentId { get; set; }

    /// <summary>
    /// 单号
    /// </summary>
    public string OrderNumber { get; set; }

    /// <summary>
    /// 流水号
    /// </summary>
    public string OrderNum { get; set; }

    /// <summary>
    /// 客户/供应商
    /// </summary>
    public string Company { get; set; }

    /// <summary>
    /// 联系方式
    /// </summary>
    public string TelPhone { get; set; }

    /// <summary>
    /// 仓库
    /// </summary>
    public string WarehouseName { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string GoodsName { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string BarCode { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string Brand { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string Specs { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string UnitName { get; set; }

    /// <summary>
    /// 出库数量
    /// </summary>
    public int? OutInCount { get; set; }

    /// <summary>
    /// 打印次数
    /// </summary>
    public int? PrintCount { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public string BatchNumber { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 操作人
    /// </summary>
    public string CreateUserName { get; set; }

    /// <summary>
    /// 打印时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// 打印人
    /// </summary>
    public string UpdateUserName { get; set; }

    /// <summary>
    /// 是否良品
    /// </summary>
    public bool? GoodProduct { get; set; }

    /// <summary>
    /// 产品类型描述（良品/次品）
    /// </summary>
    public string ProductTypeDesc => GoodProduct == true ? "良品" : GoodProduct == false ? "次品" : "未知";
}
