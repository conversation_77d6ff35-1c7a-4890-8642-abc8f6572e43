﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 发票信息表
    /// </summary>
    [SugarTable("invoicemanage", "发票信息表")]
    [Tenant("1300000000001")]
    public class InvoiceManage : EntityTenant
    {
        /// <summary>
        /// 时间
        /// </summary>
        [SugarColumn(ColumnDescription = "时间")]
        public DateTime? Time { get; set; }
        /// <summary>
        /// 发票性质 销项发票0，进项发票1
        /// </summary>
        [SugarColumn(ColumnDescription = "发票性质")]
        public int? NatureInvoice { get; set; }
        /// <summary>
        /// 发票时间
        /// </summary>
        [SugarColumn(ColumnDescription = "发票时间")]
        public DateTime? InvoiceDate { get; set; }
        /// <summary>
        /// 客户ID
        /// </summary>
        [SugarColumn(ColumnDescription = "客户ID")]
        public long? CustomId { get; set; }
        /// <summary>
        /// 来往单位
        /// </summary>
        [SugarColumn(ColumnDescription = "来往单位", Length = 50)]
        public string? ComeUnit { get; set; }
        /// <summary>
        /// 税务登记号
        /// </summary>
        [SugarColumn(ColumnDescription = "税务登记号", Length = 255)]
        public string? TaxId { get; set; }
        /// <summary>
        /// 开户行
        /// </summary>
        [SugarColumn(ColumnDescription = "开户行", Length = 255)]
        public string? BankName { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        [SugarColumn(ColumnDescription = "银行账号", Length = 255)]
        public string? BankCode { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        [SugarColumn(ColumnDescription = "地址", Length = 255)]
        public string? Address { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        [SugarColumn(ColumnDescription = "联系人", Length = 255)]
        public string? Contacts { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        [SugarColumn(ColumnDescription = "电话", Length = 255)]
        public string? Phone { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        [SugarColumn(ColumnDescription = "发票号码", Length = 50)]
        public string? InvoiceNum { get; set; }
        /// <summary>
        /// 未税金额
        /// </summary>
        [SugarColumn(ColumnDescription = "未税金额")]
        public decimal? UntaxedMoney { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        [SugarColumn(ColumnDescription = "税额")]
        public decimal? TaxAmount { get; set; }
        /// <summary>
        /// 发票金额
        /// </summary>
        [SugarColumn(ColumnDescription = "发票金额")]
        public decimal? InvoiceValue { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        [SugarColumn(ColumnDescription = "税率")]
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        [SugarColumn(ColumnDescription = "收款单号", Length = 50)]
        public string? ReceiptNum { get; set; }
        /// <summary>
        /// 付款单号
        /// </summary>
        [SugarColumn(ColumnDescription = "付款单号", Length = 50)]
        public string? PaymentNum { get; set; }
        /// <summary>
        /// 发票状态
        /// </summary>
        [SugarColumn(ColumnDescription = "发票状态")]
        public int? InvoiceStatus { get; set; }
        /// <summary>
        /// 发票种类 蓝票0，红票1
        /// </summary>
        [SugarColumn(ColumnDescription = "发票种类")]
        public int? InvoiceType { get; set; }
        /// <summary>
        /// 发票类型 普票0，专票1
        /// </summary>
        [SugarColumn(ColumnDescription = "发票类型")]
        public int? InvoiceTypeLx { get; set; }

        /// <summary>
        /// 发票地址
        /// </summary>
        [SugarColumn(ColumnDescription = "发票地址")]
        public string? InvoiceAddress { get; set; }
    }
}