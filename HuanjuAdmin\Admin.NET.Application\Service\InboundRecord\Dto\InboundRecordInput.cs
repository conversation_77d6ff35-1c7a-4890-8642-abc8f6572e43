﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 入库记录基础输入参数
    /// </summary>
    public class InboundRecordBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 入库明细Id
        /// </summary>
        public virtual long? WarehouseIncordMxId { get; set; }
        
        /// <summary>
        /// 入库数量
        /// </summary>
        public virtual int? InBoundCount { get; set; }
        
        /// <summary>
        /// 打印次数
        /// </summary>
        public virtual int? PrintCount { get; set; }
        
        /// <summary>
        /// 批次Id
        /// </summary>
        public virtual long? WarehouseBatchId { get; set; }
        
    }

    /// <summary>
    /// 入库记录分页查询输入参数
    /// </summary>
    public class InboundRecordInput : BasePageInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 入库明细Id
        /// </summary>
        public long? WarehouseIncordMxId { get; set; }
        
        /// <summary>
        /// 入库数量
        /// </summary>
        public int? InBoundCount { get; set; }
        
        /// <summary>
        /// 打印次数
        /// </summary>
        public int? PrintCount { get; set; }
        
        /// <summary>
        /// 批次Id
        /// </summary>
        public long? WarehouseBatchId { get; set; }
        
    }

    /// <summary>
    /// 入库记录增加输入参数
    /// </summary>
    public class AddInboundRecordInput : InboundRecordBaseInput
    {
    }

    /// <summary>
    /// 入库记录删除输入参数
    /// </summary>
    public class DeleteInboundRecordInput : BaseIdInput
    {
    }

    /// <summary>
    /// 入库记录更新输入参数
    /// </summary>
    public class UpdateInboundRecordInput : InboundRecordBaseInput
    {
    }

    /// <summary>
    /// 入库记录主键查询输入参数
    /// </summary>
    public class QueryByIdInboundRecordInput : DeleteInboundRecordInput
    {

    }
