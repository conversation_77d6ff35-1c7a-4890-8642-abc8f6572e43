﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商品入库明细表
    /// </summary>
    [SugarTable("warehouseinrecordmx", "商品入库明细表")]
    [Tenant("1300000000001")]
    public class WarehouseInrecordMX : EntityTenant
    {
        /// <summary>
        /// 入库单ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "入库单ID")]
        public long InrecordId { get; set; }
        /// <summary>
        /// 商品ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "商品ID")]
        public long GoodsId { get; set; }
        /// <summary>
        /// 商品
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(GoodsId))]
        public Warehousegoods Warehousegoods { get; set; }
        /// <summary>
        /// 品级
        /// </summary>
        [SugarColumn(ColumnDescription = "品级")]
        public ShangPinPinJiEnum Rating { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnDescription = "单位", Length = 10)]
        public long? Unit { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(Unit))]
        public WarehouseGoodsUnit WarehouseGoodsUnit { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnDescription = "辅助单位", Length = 10)]
        public string? AuxiliaryUnit { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [SugarColumn(ColumnDescription = "数量")]
        public int? Conuantity { get; set; }
        /// <summary>
        /// 生产日期
        /// </summary>
        [SugarColumn(ColumnDescription = "生产日期")]
        public DateTime? ProductDate { get; set; }
        /// <summary>
        /// 保质期
        /// </summary>
        [SugarColumn(ColumnDescription = "保质期")]
        public int? Shelflife { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        [SugarColumn(ColumnDescription = "单价")]
        public decimal Unitprice { get; set; }
        /// <summary>
        /// 合计
        /// </summary>
        [SugarColumn(ColumnDescription = "合计")]
        public decimal? TotalAmt { get; set; }
        /// <summary>
        /// 保质期
        /// </summary>
        [SugarColumn(ColumnDescription = "保质期单位")]
        public string ShelflifeUnit { get; set; }
        /// <summary>
        /// 过期时间
        /// </summary>
        [SugarColumn(ColumnDescription = "过期时间")]
        public DateTime? Expires { get; set; }
        /// <summary>
        /// 采购数量
        /// </summary>
        [SugarColumn(ColumnDescription = "采购数量")]
        public int? PuchQty { get; set; }
        /// <summary>
        /// 入库数量
        /// </summary>
        [SugarColumn(ColumnDescription = "入库数量")]
        public int RcvQty { get; set; }
        /// <summary>
        /// 供应商ID
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商ID")]
        public long? SupplierId { get; set; }

        /// <summary>
        /// 单据数量
        /// </summary>
        [SugarColumn(ColumnDescription = "单据数量")]
        public int? DocumentNum { get; set; }

        /// <summary>
        /// 是否良品
        /// </summary>
        [SugarColumn(ColumnDescription = "是否良品")]
        public bool GoodProduct { get; set; } = true;

        /// <summary>
        /// 实际入库数量
        /// </summary>
        [SugarColumn(ColumnDescription = "实际入库数量")]
        public int? TrueInCount { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(SupplierId))]
        public PubSupplier PubSupplier { get; set; }
    }
}