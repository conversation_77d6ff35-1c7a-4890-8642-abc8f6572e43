﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using System;
using System.Linq;

namespace Admin.NET.Application;
/// <summary>
/// 进销存汇总服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class wareInventorychangesService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<wareinventorychanges> _rep;
    private readonly SqlSugarRepository<Warehousegoods> _repWarehousegoods;
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<WarehouseInrecord> _repWarehouseInrecord;
    private readonly SqlSugarRepository<WarehouseInrecordMX> _repWarehouseInrecordMX;
    private readonly SqlSugarRepository<Warehouseout> _repWarehouseout;
    private readonly SqlSugarRepository<WarehouseoutMX> _repWarehouseoutMX;
    public wareInventorychangesService(
        UserManager userManager,
        SqlSugarRepository<Warehousegoods> repWarehousegoods,
        SqlSugarRepository<wareinventorychanges> rep,
        SqlSugarRepository<WarehouseInrecord> repWarehouseInrecord,
        SqlSugarRepository<WarehouseInrecordMX> repWarehouseInrecordMX,
        SqlSugarRepository<Warehouseout> repWarehouseout,
        SqlSugarRepository<WarehouseoutMX> repWarehouseoutMX
)
    {
        _userManager = userManager;
        _repWarehousegoods = repWarehousegoods;
        _rep = rep;
        _repWarehouseInrecord = repWarehouseInrecord;
        _repWarehouseInrecordMX = repWarehouseInrecordMX;
        _repWarehouseout = repWarehouseout;
        _repWarehouseoutMX = repWarehouseoutMX;
    }

    /// <summary>
    /// 分页查询进销存汇总
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<wareInventorychangesOutput>> Page(wareInventorychangesInput input)
    {
        var query= _rep.AsQueryable()
                    .Where(u=>u.TenantId==_userManager.TenantId)
                    .Select<wareInventorychangesOutput>()
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加进销存汇总
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddwareInventorychangesInput input)
    {
        var entity = input.Adapt<wareinventorychanges>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除进销存汇总
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpPost]
    //[ApiDescriptionSettings(Name = "Delete")]
    //public async Task Delete(DeletewareInventorychangesInput input)
    //{
    //    await _rep.FakeDeleteAsync(entity);   //假删除
    //}

    /// <summary>
    /// 更新进销存汇总
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdatewareInventorychangesInput input)
    {
        var entity = input.Adapt<wareinventorychanges>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取进销存汇总
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<wareInventorychanges> Get([FromQuery] QueryByIdwareInventorychangesInput input)
    //{
    //}

    /// <summary>
    /// 获取进销存汇总列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<wareInventorychangesOutput>> List([FromQuery] wareInventorychangesInput input)
    {
        return await _rep.AsQueryable().Select<wareInventorychangesOutput>().ToListAsync();
    }

    /// <summary>
    /// 进销存汇总
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Job")]
    public async Task Job()
    {
        DateTime now = DateTime.Now;
        DateTime firstDayOfMonth = new DateTime(now.Year, now.Month, 1);
        DateTime firstDayOfNextMonth = new DateTime(now.AddMonths(1).Year, now.AddMonths(1).Month, 1);
        //获取商品信息表
        var warehousegoods = await _repWarehousegoods.AsQueryable()
                .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                .Select<Warehousegoods>().ToListAsync();
        //获取当前月的入库明细
        var warehouseInrecords = _repWarehouseInrecordMX.AsQueryable()
                    .Where(u => u.TenantId == _userManager.TenantId)
                    .Where(u => u.CreateTime > firstDayOfMonth && u.CreateTime < firstDayOfNextMonth)
                    .Where(u => u.IsDelete == false)
                    .Select(u => new WarehouseInrecordMXOutput
                    {
                        Id = u.Id,
                        InrecordId = u.InrecordId,
                        GoodsId = u.GoodsId,
                        WarehousegoodsName = u.Warehousegoods.Name,
                        Rating = u.Rating,
                        Unit = u.Unit,
                        UnitName = u.WarehouseGoodsUnit.Name,
                        ProductDate = u.ProductDate,
                        Shelflife = u.Shelflife,
                        ShelflifeUnit = u.ShelflifeUnit,
                        Expires = u.Expires,
                        PuchQty = u.PuchQty,
                        RcvQty = u.RcvQty,
                        SupplierId = u.SupplierId,
                        PubSupplierName = u.PubSupplier.Name,
                        Unitprice = u.Unitprice,
                        TotalAmt = u.TotalAmt,
                        Barcode = u.Warehousegoods.barcode,
                        brandName = u.Warehousegoods.Brand,
                        productCode = u.Warehousegoods.Code,
                        specsName = u.Warehousegoods.Specs,
                        unique = u.Warehousegoods.isuniquecode,
                        isbatch = u.Warehousegoods.isbatch,
                        documentNum = u.DocumentNum,
                        isproduct = true
                    });
        //获取当前月的出库明细
        var warehouseOutputs = _repWarehouseoutMX.AsQueryable()
            .Where(u => u.TenantId == _userManager.TenantId && u.IsDelete == false&&u.CreateTime>firstDayOfMonth&&u.CreateTime<firstDayOfNextMonth)
            .LeftJoin<Warehouseout>((u, o) => u.OutId == o.Id)
            .LeftJoin<WarehouseStore>((u, o, s) => o.WarehouseId == s.WarehouseId && u.goodsId == s.TradeID)
            .Select((u, o, s) => new WarehouseoutMXOutput
            {
                Productcode = u.Warehousegoods.Code,
                Brand = u.Warehousegoods.Brand,
                Barcode = u.Warehousegoods.barcode,
                Specifications = u.Warehousegoods.Specs,
                UnitName = u.WarehouseGoodsUnit.Name,
                Notes = u.Notes,
                Deliverytime = u.Deliverytime,
                Id = u.Id,
                OutCount = u.OutCount,
                OutId = u.OutId,
                Tradename = u.Warehousegoods.Name,
                TrueOutCount = (int)u.TrueOutCount,
                GoodsId = u.goodsId,
                goodProduct = u.GoodProduct,
                Unit = u.Unit,
                WarehouseId = u.warehouseout.WarehouseId,
                vacancy = s.StockOrNot ?? false,
                Unitprice = u.Unitprice,
                TotalAmt = u.TotalAmt
            });
        var goodsIds= new List<long>();
        goodsIds.AddRange(warehouseInrecords.Select(u => u.GoodsId).Distinct().ToList());
        goodsIds.AddRange(warehouseOutputs.Select(u => u.GoodsId).Distinct().ToList());
        if (goodsIds.Count > 0)
        {
            List<wareinventorychanges> wareinventorychanges = new List<wareinventorychanges>();
            goodsIds.ForEach(goodsId =>
           {
               var warehouseOutputCurrent = warehouseOutputs.Where(m => m.GoodsId==goodsId).ToList();
               if (warehouseOutputCurrent.Count>0)
               {
                   wareinventorychanges wareinventorychange = new wareinventorychanges();
                   wareinventorychange.goodsId= goodsId;
                   wareinventorychange.CurrentNum = warehouseOutputCurrent.Sum(u => u.OutCount).ToDecimal();
                   wareinventorychange.CurrentMoney= warehouseOutputCurrent.Sum(u => u.TotalAmt).ToDecimal();
                   wareinventorychanges.Add(wareinventorychange);
               }
           });
           await _rep.InsertRangeAsync(wareinventorychanges);
        }
    }
}

